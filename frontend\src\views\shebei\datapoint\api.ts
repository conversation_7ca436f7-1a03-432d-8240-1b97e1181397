import { defHttp } from '/@/utils/http/axios'

enum Api {
  // PLC数据点位相关接口
  PlcDataPointList = '/shebei/plcDataPoint/list',
  PlcDataPointAdd = '/shebei/plcDataPoint/add',
  PlcDataPointEdit = '/shebei/plcDataPoint/edit',
  PlcDataPointDelete = '/shebei/plcDataPoint/delete',
  PlcDataPointDeleteBatch = '/shebei/plcDataPoint/deleteBatch',
  PlcDataPointQueryById = '/shebei/plcDataPoint/queryById',
  PlcDataPointTestRead = '/shebei/plcDataPoint/testRead',
  PlcDataPointToggleEnabled = '/shebei/plcDataPoint/toggleEnabled',
  PlcDataPointListByDevice = '/shebei/plcDataPoint/listByDevice',
  
  // 选项接口
  PlcConfigOptions = '/shebei/plcConfig/list',
  DeviceOptions = '/shebei/deviceMonitor/list',
}

/**
 * 获取PLC数据点位列表
 */
export const getPlcDataPointList = (params: any) => {
  return defHttp.get({ url: Api.PlcDataPointList, params })
}

/**
 * 添加PLC数据点位
 */
export const addPlcDataPoint = (params: any) => {
  return defHttp.post({ url: Api.PlcDataPointAdd, params })
}

/**
 * 编辑PLC数据点位
 */
export const editPlcDataPoint = (params: any) => {
  return defHttp.put({ url: Api.PlcDataPointEdit, params })
}

/**
 * 删除PLC数据点位
 */
export const deletePlcDataPoint = (params: any) => {
  return defHttp.delete({ url: Api.PlcDataPointDelete, params })
}

/**
 * 批量删除PLC数据点位
 */
export const batchDeletePlcDataPoint = (params: any) => {
  return defHttp.delete({ url: Api.PlcDataPointDeleteBatch, params })
}

/**
 * 通过ID查询PLC数据点位
 */
export const getPlcDataPointById = (params: any) => {
  return defHttp.get({ url: Api.PlcDataPointQueryById, params })
}

/**
 * 测试数据点位读取
 */
export const testDataPointRead = (id: string) => {
  return defHttp.post({ url: Api.PlcDataPointTestRead, params: { id } })
}

/**
 * 启用/禁用数据点位
 */
export const toggleDataPointEnabled = (id: string, enabled: number) => {
  return defHttp.post({ url: Api.PlcDataPointToggleEnabled, params: { id, enabled } })
}

/**
 * 根据设备ID获取数据点位列表
 */
export const getDataPointsByDevice = (deviceId: string) => {
  return defHttp.get({ url: Api.PlcDataPointListByDevice, params: { deviceId } })
}

/**
 * 获取PLC配置选项
 */
export const getPlcConfigOptions = () => {
  return defHttp.get({ 
    url: Api.PlcConfigOptions, 
    params: { pageNo: 1, pageSize: 1000, enabled: 1 } 
  }).then(res => res.result?.records || [])
}

/**
 * 获取设备选项
 */
export const getDeviceOptions = () => {
  return defHttp.get({ 
    url: Api.DeviceOptions, 
    params: { pageNo: 1, pageSize: 1000 } 
  }).then(res => res.result?.records || [])
}
