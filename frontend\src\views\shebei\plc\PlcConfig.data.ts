import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Table'

// 列表页面公共参数
export const columns: BasicColumn[] = [
  {
    title: 'PLC名称',
    align: 'center',
    dataIndex: 'plcName',
    width: 150,
  },
  {
    title: 'IP地址',
    align: 'center',
    dataIndex: 'ipAddress',
    width: 120,
  },
  {
    title: '端口',
    align: 'center',
    dataIndex: 'port',
    width: 80,
  },
  {
    title: '机架号',
    align: 'center',
    dataIndex: 'rack',
    width: 80,
  },
  {
    title: '插槽号',
    align: 'center',
    dataIndex: 'slot',
    width: 80,
  },
  {
    title: '启用状态',
    align: 'center',
    dataIndex: 'enabled',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '禁用', color: '#ff4d4f' },
        1: { text: '启用', color: '#52c41a' }
      }
      const status = statusMap[text] || { text: '未知', color: '#999' }
      return `<span style="color: ${status.color}">${status.text}</span>`
    },
  },
  {
    title: '连接状态',
    align: 'center',
    dataIndex: 'connectionStatus',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '断开', color: '#ff4d4f' },
        1: { text: '连接', color: '#52c41a' }
      }
      const status = statusMap[text] || { text: '未知', color: '#999' }
      return `<span style="color: ${status.color}">${status.text}</span>`
    },
  },
  {
    title: '最后连接时间',
    align: 'center',
    dataIndex: 'lastConnectionTime',
    width: 160,
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    fixed: 'right',
    width: 200,
    slots: { customRender: 'action' },
  },
]

// 表单数据
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: 'PLC名称',
    field: 'plcName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入PLC名称',
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入PLC名称!' },
        { max: 100, message: 'PLC名称不能超过100个字符!' },
      ]
    },
  },
  {
    label: 'IP地址',
    field: 'ipAddress',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入IP地址，如：*************',
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入IP地址!' },
        { 
          pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
          message: '请输入正确的IP地址格式!'
        },
      ]
    },
  },
  {
    label: '端口',
    field: 'port',
    component: 'InputNumber',
    defaultValue: 102,
    componentProps: {
      placeholder: '请输入端口号',
      min: 1,
      max: 65535,
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入端口号!' },
      ]
    },
  },
  {
    label: '机架号',
    field: 'rack',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: {
      placeholder: '请输入机架号',
      min: 0,
      max: 7,
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入机架号!' },
      ]
    },
  },
  {
    label: '插槽号',
    field: 'slot',
    component: 'InputNumber',
    defaultValue: 1,
    componentProps: {
      placeholder: '请输入插槽号',
      min: 0,
      max: 31,
    },
    dynamicRules: ({ model, schema }) => {
      return [
        { required: true, message: '请输入插槽号!' },
      ]
    },
  },
  {
    label: '连接超时(毫秒)',
    field: 'connectionTimeout',
    component: 'InputNumber',
    defaultValue: 5000,
    componentProps: {
      placeholder: '请输入连接超时时间',
      min: 1000,
      max: 30000,
    },
  },
  {
    label: '读取超时(毫秒)',
    field: 'readTimeout',
    component: 'InputNumber',
    defaultValue: 3000,
    componentProps: {
      placeholder: '请输入读取超时时间',
      min: 1000,
      max: 30000,
    },
  },
  {
    label: '启用状态',
    field: 'enabled',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    required: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 500,
    },
  },
]
