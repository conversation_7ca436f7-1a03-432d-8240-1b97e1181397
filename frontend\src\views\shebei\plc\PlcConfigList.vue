<template>
  <div>
    <!-- 查询区域 -->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="8">
            <a-form-item label="PLC名称" name="plcName">
              <a-input placeholder="请输入PLC名称" v-model:value="queryParam.plcName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item label="IP地址" name="ipAddress">
              <a-input placeholder="请输入IP地址" v-model:value="queryParam.ipAddress"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item label="启用状态" name="enabled">
              <a-select placeholder="请选择启用状态" v-model:value="queryParam.enabled" allowClear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <span style="float: left; overflow: hidden;" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                <a @click="handleToggleSearch" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <span v-if="toggleSearchStatus">↑</span>
                  <span v-else>↓</span>
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="jeecg-basic-table-form-container">
      <div class="anty-form-btn">
        <a-button @click="handleAdd" type="primary" icon="plus">新增PLC</a-button>
        <a-button type="primary" icon="download" @click="handleExportXls('PLC配置')">导出</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作 ▼</a-button>
        </a-dropdown>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="jeecg-basic-table-container">
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" @click="handleTestConnection(record)">测试连接</a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="handleToggleEnabled(record)"
              :style="{ color: record.enabled === 1 ? '#ff4d4f' : '#52c41a' }">
              {{ record.enabled === 1 ? '禁用' : '启用' }}
            </a-button>
            <a-popconfirm
              title="确定删除吗?"
              @confirm="handleDelete(record)"
              okText="确定"
              cancelText="取消">
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>

      </a-table>
    </div>

    <!-- 表单区域 -->
    <PlcConfigModal ref="modalRef" @success="handleSuccess"></PlcConfigModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { useMessage } from '/@/hooks/web/useMessage'
  import PlcConfigModal from './components/PlcConfigModal.vue'
  import { columns } from './PlcConfig.data'
  import { getPlcConfigList, deletePlcConfig, batchDeletePlcConfig, testPlcConnection, togglePlcEnabled } from './api'

  const { createMessage } = useMessage()
  
  // 响应式数据
  const loading = ref(false)
  const dataSource = ref([])
  const selectedRowKeys = ref([])
  const toggleSearchStatus = ref(false)
  const modalRef = ref()
  
  // 查询参数
  const queryParam = reactive({
    plcName: '',
    ipAddress: '',
    enabled: undefined,
  })
  
  // 分页参数
  const ipagination = reactive({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
      return range[0] + '-' + range[1] + ' 共' + total + '条'
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: 0
  })
  
  // 表单配置
  const labelCol = { span: 5 }
  const wrapperCol = { span: 18, offset: 1 }

  // 加载数据
  const loadData = async (arg?: any) => {
    if (arg === 1) {
      ipagination.current = 1
    }
    const params = Object.assign({}, queryParam, {
      pageNo: ipagination.current,
      pageSize: ipagination.pageSize,
    })
    loading.value = true
    try {
      const result = await getPlcConfigList(params)
      if (result.success) {
        dataSource.value = result.result.records
        ipagination.total = result.result.total
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 查询
  const searchQuery = () => {
    loadData(1)
  }

  // 重置
  const searchReset = () => {
    Object.keys(queryParam).forEach(key => {
      queryParam[key] = undefined
    })
    loadData(1)
  }

  // 切换搜索状态
  const handleToggleSearch = () => {
    toggleSearchStatus.value = !toggleSearchStatus.value
  }

  // 表格变化
  const handleTableChange = (pagination) => {
    ipagination.current = pagination.current
    ipagination.pageSize = pagination.pageSize
    loadData()
  }

  // 选择变化
  const onSelectChange = (keys) => {
    selectedRowKeys.value = keys
  }

  // 新增
  function handleAdd() {
    modalRef.value.add()
  }

  // 编辑
  function handleEdit(record) {
    modalRef.value.edit(record)
  }

  // 删除
  async function handleDelete(record) {
    try {
      await deletePlcConfig({ id: record.id })
      createMessage.success('删除成功!')
      handleSuccess()
    } catch (error) {
      createMessage.error('删除失败!')
    }
  }

  // 批量删除
  async function batchHandleDelete() {
    try {
      await batchDeletePlcConfig({ ids: selectedRowKeys.value.join(',') })
      createMessage.success('批量删除成功!')
      handleSuccess()
    } catch (error) {
      createMessage.error('批量删除失败!')
    }
  }

  // 测试连接
  async function handleTestConnection(record) {
    try {
      loading.value = true
      const result = await testPlcConnection(record.id)
      if (result.success) {
        createMessage.success('连接测试成功!')
      } else {
        createMessage.error(result.message || '连接测试失败!')
      }
    } catch (error) {
      createMessage.error('连接测试异常!')
    } finally {
      loading.value = false
    }
  }

  // 启用/禁用
  async function handleToggleEnabled(record) {
    try {
      const newStatus = record.enabled === 1 ? 0 : 1
      await togglePlcEnabled(record.id, newStatus)
      createMessage.success(newStatus === 1 ? '启用成功!' : '禁用成功!')
      handleSuccess()
    } catch (error) {
      createMessage.error('操作失败!')
    }
  }

  // 成功回调
  function handleSuccess() {
    selectedRowKeys.value = []
    loadData()
  }

  // 导出
  const handleExportXls = (fileName) => {
    console.log('导出:', fileName)
  }

  // 初始化
  loadData()
</script>

<style scoped>

</style>
