import { defHttp } from '/@/utils/http/axios'

enum Api {
  // 设备监控相关接口
  DeviceMonitorList = '/shebei/deviceMonitor/list',
  DeviceMonitorAdd = '/shebei/deviceMonitor/add',
  DeviceMonitorEdit = '/shebei/deviceMonitor/edit',
  DeviceMonitorDelete = '/shebei/deviceMonitor/delete',
  DeviceMonitorDeleteBatch = '/shebei/deviceMonitor/deleteBatch',
  DeviceMonitorQueryById = '/shebei/deviceMonitor/queryById',
  DeviceMonitorDashboard = '/shebei/deviceMonitor/dashboard',
  DeviceMonitorLatestData = '/shebei/deviceMonitor/latestData',
  DeviceMonitorHistoryData = '/shebei/deviceMonitor/historyData',
  DeviceMonitorTrendData = '/shebei/deviceMonitor/trendData',
  DeviceMonitorUpdateStatus = '/shebei/deviceMonitor/updateStatus',
}

/**
 * 获取设备监控列表
 */
export const getDeviceMonitorList = (params: any) => {
  return defHttp.get({ url: Api.DeviceMonitorList, params })
}

/**
 * 添加设备监控
 */
export const addDeviceMonitor = (params: any) => {
  return defHttp.post({ url: Api.DeviceMonitorAdd, params })
}

/**
 * 编辑设备监控
 */
export const editDeviceMonitor = (params: any) => {
  return defHttp.put({ url: Api.DeviceMonitorEdit, params })
}

/**
 * 删除设备监控
 */
export const deleteDeviceMonitor = (params: any) => {
  return defHttp.delete({ url: Api.DeviceMonitorDelete, params })
}

/**
 * 批量删除设备监控
 */
export const batchDeleteDeviceMonitor = (params: any) => {
  return defHttp.delete({ url: Api.DeviceMonitorDeleteBatch, params })
}

/**
 * 通过ID查询设备监控
 */
export const getDeviceMonitorById = (params: any) => {
  return defHttp.get({ url: Api.DeviceMonitorQueryById, params })
}

/**
 * 获取设备监控大屏数据
 */
export const getDashboardData = () => {
  return defHttp.get({ url: Api.DeviceMonitorDashboard })
}

/**
 * 获取设备最新监控数据
 */
export const getLatestData = (deviceId: string) => {
  return defHttp.get({ url: Api.DeviceMonitorLatestData, params: { deviceId } })
}

/**
 * 获取设备历史监控数据
 */
export const getHistoryData = (deviceId: string, hours: number = 24) => {
  return defHttp.get({ url: Api.DeviceMonitorHistoryData, params: { deviceId, hours } })
}

/**
 * 获取设备监控趋势数据
 */
export const getTrendData = (deviceId: string, hours: number = 24) => {
  return defHttp.get({ url: Api.DeviceMonitorTrendData, params: { deviceId, hours } })
}

/**
 * 更新设备状态
 */
export const updateDeviceStatus = (deviceId: string, status: number, runStatus: number) => {
  return defHttp.post({ 
    url: Api.DeviceMonitorUpdateStatus, 
    params: { deviceId, status, runStatus } 
  })
}
