# PLC数据采集配置
plc:
  data:
    collection:
      # 是否启用PLC数据采集
      enabled: true
      # 数据采集间隔(秒)
      interval: 30
      # 连接超时时间(毫秒)
      connection-timeout: 5000
      # 读取超时时间(毫秒)
      read-timeout: 3000
      # 重试次数
      retry-count: 3
      # 重试间隔(毫秒)
      retry-interval: 1000
      # 连接池大小
      pool-size: 10
      # 是否启用连接池
      pool-enabled: true
      # 连接保持时间(分钟)
      keep-alive: 30
      
  # S7协议配置
  s7:
    # 默认端口
    default-port: 102
    # 默认机架号
    default-rack: 0
    # 默认插槽号
    default-slot: 1
    # PDU大小
    pdu-size: 240
    
  # 日志配置
  logging:
    # 是否启用详细日志
    verbose: false
    # 是否记录数据采集日志
    data-log: true
    # 是否记录连接日志
    connection-log: true
